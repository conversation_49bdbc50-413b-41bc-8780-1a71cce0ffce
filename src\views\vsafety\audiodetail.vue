<template>
  <div class="mod-vsafety__audiodetail">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <!-- 新增的筛选查询组件 -->
      <el-form-item>
        <el-select v-model="state.dataForm.sentimentAnalysis" placeholder="请选择情感倾向性" clearable>
          <el-option label="正面" value="正面"></el-option>
          <el-option label="负面" value="负面"></el-option>
          <el-option label="中性" value="中性"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-select v-model="state.dataForm.eventCategory" placeholder="请选择事件分类" clearable>
          <el-option label="政治舆情" value="政治舆情"></el-option>
          <el-option label="经济舆情" value="经济舆情"></el-option>
          <el-option label="文化舆情" value="文化舆情"></el-option>
          <el-option label="社会舆情" value="社会舆情"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="state.getDataList()" :icon="Search">
          查询
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="resetQuery()" :icon="Refresh">
          重置
        </el-button>
      </el-form-item>
      <!-- 筛选查询组件结束 -->
      
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
          新增
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button 
          type="danger" 
          @click="state.deleteHandle()" 
          :icon="Delete"
          :disabled="state.dataListSelections && !state.dataListSelections.length"
        >
          删除
        </el-button>
      </el-form-item>
    </el-form>
    
    <el-table 
      v-loading="state.dataListLoading" 
      :data="state.dataList" 
      border 
      @selection-change="state.dataListSelectionChangeHandle" 
      style="width: 100%"
      stripe
    >
      <el-table-column 
        type="selection" 
        header-align="center" 
        align="center" 
        width="50"
      ></el-table-column>
      <el-table-column prop="id" type="index" label="ID" header-align="center" align="center" width="100"></el-table-column>
      
      <!-- 音频预览列 -->
      <el-table-column label="音频预览" header-align="center" align="center">
        <template v-slot="scope">
          <audio 
            v-if="scope.row.url" 
            controls 
            :src="scope.row.url"
            :data-id="scope.row.id"  
            @play="handleAudioPlay(scope.row)"
            @pause="handleAudioPause(scope.row)"
            @ended="handleAudioEnded(scope.row)"
            class="custom-audio-player"
          >
            您的浏览器不支持音频播放
          </audio>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="url" label="音频链接" header-align="center" align="center" width="300">
        <template v-slot="scope">
          <template v-if="scope.row.url">
            <el-link
              type="primary"
              :href="scope.row.url"
              target="_blank"
              :underline="false"
              class="url-link"
            >
              {{ truncateUrl(scope.row.url) }}
            </el-link>
          </template>
          <template v-else>
            <span class="disabled-link">无链接</span>
          </template>
        </template>
      </el-table-column>
      
      <el-table-column label="音频分析" header-align="center" align="center" width="200">
        <template v-slot="scope">
          <div class="audio-analysis-container">
            <el-button 
              v-if="!scope.row.analysisResult"
              type="primary" 
              @click="analyzeExistingAudio(scope.row)"
              :loading="scope.row.analyzing"
              size="small"
              :disabled="!scope.row.url"
            >
              分析音频
            </el-button>
          </div>
          
          <!-- 查看分析结果按钮 -->
          <el-button 
            v-if="scope.row.analysisResult"
            type="info" 
            @click="showAnalysisResult(scope.row)"
            size="small"
            link
          >
            查看分析结果
          </el-button>
        </template>
      </el-table-column>
      
      <el-table-column 
        label="操作" 
        fixed="right" 
        header-align="center" 
        align="center" 
        width="200"
      >
        <template v-slot="scope">
          <el-button 
            type="primary" 
            link 
            @click="addOrUpdateHandle(scope.row.id)"
            :icon="Edit"
            class="action-btn"
          >
            修改
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="state.deleteHandle(scope.row.id)"
            :icon="Delete"
            class="action-btn"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination 
      :current-page="state.page" 
      :page-sizes="[10, 20, 50, 100]" 
      :page-size="state.limit" 
      :total="state.total" 
      layout="total, sizes, prev, pager, next, jumper" 
      @size-change="state.pageSizeChangeHandle" 
      @current-change="state.pageCurrentChangeHandle"
      style="margin-top: 20px;"
    ></el-pagination>
    
    <!-- 音频上传弹窗 -->
    <el-dialog 
      v-model="uploadDialogVisible" 
      title="上传音频" 
      width="30%"
      :close-on-click-modal="false"
    >
      <div class="upload-container">
        <input 
          ref="fileInputRef" 
          type="file" 
          accept="audio/*" 
          @change="handleFileChange" 
          style="display: none"
        />
        <div class="upload-area" @click="triggerFileInput" v-if="!selectedFile">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <p>点击选择音频文件</p>
        </div>
        <div class="file-selected" v-else>
          <el-icon class="file-icon"><Document /></el-icon>
          <div class="file-info">
            <p class="file-name">{{ selectedFileName }}</p>
            <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
          </div>
          <el-icon class="change-icon" @click.stop="triggerFileInput"><Refresh /></el-icon>
        </div>
      </div>
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="uploadAudio" 
          :loading="uploadLoading"
          :disabled="!selectedFile"
        >
          上传
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    
    <!-- 分析结果弹窗 -->
    <el-dialog
      v-model="analysisDialogVisible"
      width="65vw"
      :before-close="handleAnalysisDialogClose"
      class="analysis-result-dialog"
      style="height: 75vh"
    >
      <template #header>
        <div class="dialog-header">
          <span class="el-dialog__title">音频分析结果</span>
        </div>
      </template>
      
      <!-- 添加滚动条包装 -->
      <el-scrollbar class="analysis-dialog-scrollbar">
        <div v-if="currentAnalysisResult" class="analysis-result-dialog">
          
          <!-- 音频播放和重新分析按钮容器 -->
          <div v-if="currentAudioUrl" class="audio-preview-container">
            <div class="audio-player-wrapper">
              <audio 
                controls 
                :src="currentAudioUrl"
                class="dialog-audio-player"
              >
                您的浏览器不支持音频播放
              </audio>
              <el-button 
                v-if="currentAnalysisRow"
                type="primary" 
                @click="reanalyzeAudio"
                :loading="currentAnalysisRow.analyzing"
                size="default"
                class="reanalyze-btn"
                round
              >
                <el-icon><Refresh /></el-icon>
                重新分析
              </el-button>
            </div>
          </div>
          
          <!-- 事件数据展示 -->
          <div class="events-container">
            <!-- 标签展示 -->
            <el-divider content-position="left">关键词</el-divider>
            <div v-if="parseTagsData(currentAnalysisResult).length > 0" class="tags-container">
              <el-tag 
                v-for="(tag, index) in parseTagsData(currentAnalysisResult)" 
                :key="index" 
                type="primary" 
                size="large"
                class="keyword-tag"
              >
                {{ tag }}
              </el-tag>
            </div>
            <div v-else>
              <el-empty description="暂无关键词数据" :image-size="80" />
            </div>
            
            <!-- 事件信息展示 -->
            <el-divider content-position="left">事件信息</el-divider>
            <div v-if="parseEventsData(currentAnalysisResult).length > 0">
              <el-card 
                v-for="(event, index) in parseEventsData(currentAnalysisResult)" 
                :key="index" 
                class="event-card"
                shadow="hover"
              >
                <template #header>
                  <div class="event-header">
                    <span class="event-title">{{ event.event_title }}</span>
                    <el-tag type="primary" size="small">ID: {{ event.event_id }}</el-tag>
                  </div>
                </template>
                
                <!-- 添加舆论情感和舆情分类字段 -->
                <div class="sentiment-category-info">
                  <div class="info-item">
                    <span class="label">舆论情感:</span>
                    <el-tag 
                      :type="getSentimentType(event.public_opinion_sentiment)" 
                      size="small"
                      style="margin-left: 7px; font-size: 14px; padding: 5px 10px;"
                    >
                      {{ event.public_opinion_sentiment || '无' }}
                    </el-tag>
                    <span class="label" style="margin-left: 20px;">舆情分类:</span>
                    <el-tag 
                      type="success" 
                      size="small" 
                      style="margin-left: 7px; font-size: 14px; padding: 5px 10px;"
                    >
                      {{ event.public_opinion_category || '无' }}
                    </el-tag>
                  </div>
                </div>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>地点:</label>
                      <span>{{ event.location }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>时间:</label>
                      <span>{{ event.time }}</span>
                    </div>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>参与者:</label>
                      <span>
                        <el-tag 
                          v-for="(participant, pIndex) in event.participants" 
                          :key="pIndex" 
                          size="small" 
                          class="participant-tag"
                        >
                          {{ participant }}
                        </el-tag>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>环境:</label>
                      <span>{{ event.audio_environment }}</span>
                    </div>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>事件描述:</label>
                      <span>{{ event.event_description }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>音频分析:</label>
                      <span>{{ event.sound_analysis }}</span>
                    </div>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>事件原因:</label>
                      <span>{{ event.event_cause }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>事件结果:</label>
                      <span>{{ event.event_outcome }}</span>
                    </div>
                  </el-col>
                </el-row>
                
                <!-- 新增详细分类和负面热点字段 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>详细分类:</label>
                      <span>
                        <template v-if="event.detailed_category && event.detailed_category.length > 0">
                          <el-tag 
                            v-for="(category, cIndex) in event.detailed_category" 
                            :key="cIndex" 
                            size="small" 
                            class="participant-tag"
                            type="success"
                          >
                            {{ category }}
                          </el-tag>
                        </template>
                        <span v-else>无</span>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="event-info-item">
                      <label>负面热点:</label>
                      <span>{{ event.negative_hotspot_category || '无' }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </div>
            <div v-else>
              <el-empty description="暂无事件数据" :image-size="80" />
            </div>
            
            <!-- 音频分析展示 -->
            <el-divider content-position="left">音频分析</el-divider>
            <div v-if="parseVisualElementsData(currentAnalysisResult).length > 0" class="visual-elements-container">
              <div class="timeline-container">
                <div 
                  v-for="(element, index) in parseVisualElementsData(currentAnalysisResult)" 
                  :key="index" 
                  class="timeline-item"
                >
                  <div class="timeline-marker"></div>
                  <div class="timeline-content">
                    <div class="element-info-item">
                      <label>描述:</label>
                      <span>{{ element.event }}</span>
                    </div>
                    <div class="element-info-item">
                      <label>时间:</label>
                      <el-tag type="success" size="small" class="timestamp-tag">{{ element.timestamp }}</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              <el-empty description="暂无音频分析数据" :image-size="80" />
            </div>
          </div>
        </div>
        <div v-else>
          <el-empty description="暂无分析结果" />
        </div>
      </el-scrollbar>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="analysisDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, onBeforeUnmount } from "vue";
import AddOrUpdate from "./audio-add-or-update.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { Plus, Delete, Edit, Link, Document, Refresh, Search } from "@element-plus/icons-vue";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/vsafety/audio/page",
  getDataListIsPage: true,
  exportURL: "/vsafety/audio/export",
  deleteURL: "/vsafety/audio"
});

const state = reactive({ 
  ...useView(view), 
  ...toRefs(view),
  // 重写 getDataList 方法
  getDataList() {
    // 判断是否需要调用分类查询接口
    if (state.dataForm?.sentimentAnalysis || state.dataForm?.eventCategory) {
      // 调用分类查询接口
      state.getCategoryList();
    } else {
      // 调用原始分页接口
      state.defaultGetList();
    }
  },
  // 新增分类查询方法
  async getCategoryList() {
    state.dataListLoading = true;
    try {
      const params = {
        page: state.page,
        limit: state.limit,
        orderField: state.orderField,
        order: state.order,
        ...state.dataForm
      };
      
      const res = await baseService.post("/vsafety/audio/categoryPage", params);
      if (res.code === 0) {
        state.dataList = res.data.list;
        state.total = res.data.total;
      } else {
        state.dataList = [];
        state.total = 0;
        ElMessage.error(res.msg);
      }
    } catch (error) {
      state.dataList = [];
      state.total = 0;
      ElMessage.error("请求失败");
    } finally {
      state.dataListLoading = false;
    }
  },
  // 保存原始 getList 方法
  defaultGetList: useView(view).getDataList
});

// 添加重置查询方法
const resetQuery = () => {
  // 确保 dataForm 存在并重置所有筛选条件
  if (!state.dataForm) {
    state.dataForm = {};
  }
  state.dataForm.sentimentAnalysis = '';
  state.dataForm.eventCategory = '';
  state.page = 1;
  // 调用修改后的getList方法而不是defaultGetList
  state.getDataList();
};

// 添加弹窗相关状态
const analysisDialogVisible = ref(false);
const currentAnalysisResult = ref<string | null>(null);
const currentAudioUrl = ref<string | null>(null);
const currentAnalysisRow = ref<any>(null); // 添加这一行来存储当前分析的行数据

// 音频上传相关状态
const uploadDialogVisible = ref(false);
const fileInputRef = ref<HTMLInputElement | null>(null);
const selectedFile = ref<File | null>(null);
const selectedFileName = ref("");
const uploadLoading = ref(false);

// 分析现有音频
const analyzeExistingAudio = async (row: any) => {
  try {
    row.analyzing = true;
    
    // 从URL获取音频文件
    const response = await fetch(row.url);
    const blob = await response.blob();
    
    // 从URL中提取干净的文件名，去除查询参数
    const urlWithoutParams = row.url.split('?')[0];
    let filename = urlWithoutParams.split('/').pop() || 'audio.mp3';
    
    // 确保文件名有正确的扩展名
    if (!filename.includes('.')) {
      // 根据blob类型添加适当的扩展名
      if (blob.type.startsWith('audio/mp4')) {
        filename += '.mp4';
      } else if (blob.type.startsWith('audio/mpeg')) {
        filename += '.mp3';
      } else if (blob.type.startsWith('audio/wav')) {
        filename += '.wav';
      } else if (blob.type.startsWith('audio/ogg')) {
        filename += '.ogg';
      } else {
        // 默认使用mp3扩展名
        filename += '.mp3';
      }
    }
    
    const file = new File([blob], filename, { type: blob.type });
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('id', row.id); // 添加id参数
    
    const res = await baseService.post("/vsafety/audio/analysis", formData);
    
    if (res.code === 0) {
      // 获取最新数据
      const updatedData = await baseService.get(`/vsafety/audio/${row.id}`);
      if (updatedData.code === 0) {
        // 更新当前行的分析结果
        row.analysisResult = updatedData.data.analysisResult;
        ElMessage.success("音频分析完成");
      }
    } else {
      ElMessage.error(res.msg || "音频分析失败");
    }
  } catch (err) {
    ElMessage.error("音频分析失败: " + (err as Error).message);
  } finally {
    row.analyzing = false;
  }
};

// 通用数据解析函数
const parseData = (result: string, key: string): any[] => {
  try {
    // 如果result是字符串，先解析为JSON对象
    let parsed;
    if (typeof result === 'string') {
      parsed = JSON.parse(result);
    } else {
      parsed = result;
    }
    
    // 如果parsed是数组且第一个元素有指定的key
    if (Array.isArray(parsed) && parsed.length > 0 && parsed[0][key]) {
      return parsed[0][key];
    }
    
    // 如果parsed有指定的key且是数组，返回该key对应的值
    if (parsed[key] && Array.isArray(parsed[key])) {
      return parsed[key];
    }
    
    return [];
  } catch (error) {
    console.error(`解析${key}数据出错:`, error);
    return [];
  }
};

// 解析事件数据
const parseEventsData = (result: string): any[] => {
  return parseData(result, 'events');
};

// 解析视觉元素数据
const parseVisualElementsData = (result: string): any[] => {
  return parseData(result, 'timeline');
};

// 解析标签数据
const parseTagsData = (result: string): string[] => {
  return parseData(result, 'tags');
};

// 处理弹窗关闭
const handleAnalysisDialogClose = () => {
  analysisDialogVisible.value = false;
  currentAnalysisResult.value = null;
  currentAudioUrl.value = null;
};

// 显示分析结果
const showAnalysisResult = async (row: any) => {
  // 如果有分析结果直接显示，否则先获取
  if (row.analysisResult) {
    currentAnalysisResult.value = row.analysisResult;
    currentAudioUrl.value = row.url;
    currentAnalysisRow.value = row; // 保存当前行数据
    analysisDialogVisible.value = true;
  } else {
    // 重新获取数据
    try {
      const res = await baseService.get(`/vsafety/audio/${row.id}`);
      if (res.code === 0 && res.data.analysisResult) {
        currentAnalysisResult.value = res.data.analysisResult;
        currentAudioUrl.value = row.url;
        currentAnalysisRow.value = row; // 保存当前行数据
        analysisDialogVisible.value = true;
      } else {
        ElMessage.warning("暂无分析结果");
      }
    } catch (err) {
      ElMessage.error("获取分析结果失败: " + (err as Error).message);
    }
  }
};

// 添加重新分析方法
const reanalyzeAudio = async () => {
  if (!currentAnalysisRow.value) return;
  
  try {
    const row = currentAnalysisRow.value;
    row.analyzing = true;
    
    // 从URL获取音频文件
    const response = await fetch(row.url);
    const blob = await response.blob();
    
    // 从URL中提取干净的文件名，去除查询参数
    const urlWithoutParams = row.url.split('?')[0];
    let filename = urlWithoutParams.split('/').pop() || 'audio.mp3';
    
    // 确保文件名有正确的扩展名
    if (!filename.includes('.')) {
      // 根据blob类型添加适当的扩展名
      if (blob.type.startsWith('audio/mp4')) {
        filename += '.mp4';
      } else if (blob.type.startsWith('audio/mpeg')) {
        filename += '.mp3';
      } else if (blob.type.startsWith('audio/wav')) {
        filename += '.wav';
      } else if (blob.type.startsWith('audio/ogg')) {
        filename += '.ogg';
      } else {
        // 默认使用mp3扩展名
        filename += '.mp3';
      }
    }
    
    const file = new File([blob], filename, { type: blob.type });
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('id', row.id); // 添加id参数
    
    const res = await baseService.post("/vsafety/audio/analysis", formData);
    
    if (res.code === 0) {
      // 获取最新数据
      const updatedData = await baseService.get(`/vsafety/audio/${row.id}`);
      if (updatedData.code === 0) {
        // 更新当前行的分析结果
        row.analysisResult = updatedData.data.analysisResult;
        ElMessage.success("音频分析完成");
        // 更新弹窗中的分析结果
        currentAnalysisResult.value = updatedData.data.analysisResult;
      }
    } else {
      ElMessage.error(res.msg || "音频分析失败");
    }
  } catch (err) {
    ElMessage.error("音频分析失败: " + (err as Error).message);
  } finally {
    if (currentAnalysisRow.value) {
      currentAnalysisRow.value.analyzing = false;
    }
  }
};

// 音频播放状态管理
const currentPlayingId = ref(null);

// 处理音频播放事件
const handleAudioPlay = (row: any) => {
  // 停止其他所有正在播放的音频
  document.querySelectorAll('audio').forEach(audio => {
    if (audio.getAttribute('data-id') !== String(row.id) && !audio.paused) {
      audio.pause();
    }
  });
  
  currentPlayingId.value = row.id;
};

// 处理音频暂停事件
const handleAudioPause = (row: any) => {
  if (currentPlayingId.value === row.id) {
    currentPlayingId.value = null;
  }
};

// 处理音频播放结束事件
const handleAudioEnded = (row: any) => {
  if (currentPlayingId.value === row.id) {
    currentPlayingId.value = null;
  }
};

// 组件卸载时停止所有音频
onBeforeUnmount(() => {
  document.querySelectorAll('audio').forEach(audio => audio.pause());
});

// 音频上传相关函数
const addOrUpdateRef = ref();

const addOrUpdateHandle = (id?: number) => {
  // 如果有id，则是修改操作，使用原来的弹窗
  if (id) {
    addOrUpdateRef.value.init(id);
  } else {
    // 否则是新增操作，打开上传弹窗
    uploadDialogVisible.value = true;
    selectedFile.value = null;
    selectedFileName.value = "";
  }
};

// 触发文件选择
const triggerFileInput = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// 处理文件选择
const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    
    // 验证文件类型是否为音频文件
    const allowedTypes = [
      'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg', 
      'audio/aac', 'audio/flac', 'audio/m4a', 'audio/x-m4a',
      'audio/mp4'
    ];
    
    const allowedExtensions = [
      '.mp3', '.wav', '.ogg', '.aac', '.flac', '.m4a', '.mp4'
    ];
    
    // 检查 MIME 类型或文件扩展名
    const isAllowedType = allowedTypes.includes(file.type);
    const isAllowedExtension = allowedExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );
    
    if (!isAllowedType && !isAllowedExtension) {
      ElMessage.error("请选择有效的音频文件 (mp3, wav, ogg, aac, flac, m4a, mp4)");
      // 清空文件输入框
      input.value = '';
      return;
    }
    
    selectedFile.value = file;
    selectedFileName.value = file.name;
  }
};

// 上传音频文件
const uploadAudio = async () => {
  if (!selectedFile.value) {
    ElMessage.warning("请选择音频文件");
    return;
  }

  uploadLoading.value = true;
  
  try {
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    
    const res = await baseService.post("/vsafety/audio/upload", formData);
    
    if (res.code === 0) {
      ElMessage.success("音频上传成功");
      uploadDialogVisible.value = false;
      // 刷新音频分页数据
      state.getDataList();
    } else {
      ElMessage.error(res.msg || "音频上传失败");
    }
  } catch (error: any) {
  
  } finally {
    uploadLoading.value = false;
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 添加获取情感类型的方法
const getSentimentType = (sentiment: string) => {
  switch (sentiment) {
    case '正面':
      return 'success';
    case '负面':
      return 'danger';
    case '中性':
      return 'warning';
    default:
      return 'info';
  }
};

// 截断URL显示函数
const truncateUrl = (url: string): string => {
  if (!url) return '';
  const maxLength = 50; // 最大显示长度
  if (url.length <= maxLength) {
    return url;
  }
  return url.substring(0, maxLength) + '...';
};

</script>

<style lang="scss" scoped>
.mod-vsafety__audiodetail {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  /* 分析结果弹窗样式 */
  .analysis-result-dialog {
    &.el-dialog {
      max-width: 800px;
      margin: 0 auto;
      
      .el-dialog__body {
        padding: 0; // 移除默认padding
      }
    }
    
    // 添加滚动条样式
    .analysis-dialog-scrollbar {
      height: calc(75vh - 110px); // 计算可用高度，减去header和footer的高度
    }
    
    .audio-preview-container {
      margin-bottom: 20px;
      
      .audio-player-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-left: 15%;
        margin-top: 0px;
      }
      
      .dialog-audio-player {
        width: 100%;
        max-width: 600px;
        outline: none;
        
        &::-webkit-media-controls-panel {
          background-color: #f5f7fa;
          border-radius: 4px;
        }
        
        &::-webkit-media-controls-play-button {
          background-color: #409EFF;
          color: white;
          border-radius: 50%;
        }
      }
    }
    
    .events-container {
      margin-top: 20px;
      
      // 添加舆论情感和舆情分类样式
      .sentiment-category-info {
        margin-bottom: 15px;
        padding: 10px 0;
        
        .info-item {
          display: flex;
          align-items: center;
          
          .label {
            font-weight: bold;
            color: #606266;
            font-size: 14px;
          }
        }
      }
      
      // 标签容器样式
      .tags-container {
        padding: 15px 0;
        min-height: 60px;
        
        .keyword-tag {
          margin-right: 10px;
          margin-bottom: 10px;
          padding: 10px 15px;
          font-size: 14px;
          border-radius: 20px;
          background-color: #ecf5ff;
          color: #409eff;
          border-color: #d9ecff;
          
          &:hover {
            background-color: #409eff;
            color: white;
            border-color: #409eff;
          }
        }
      }
      
      .event-card {
        margin-bottom: 15px;
        border-radius: 8px;
        border: 1px solid #ebeef5;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }
        
        .event-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .event-title {
            font-weight: bold;
            font-size: 16px;
            color: #303133;
          }
        }
        
        .event-info-item {
          display: flex;
          margin-bottom: 12px;
          line-height: 1.6;
          
          label {
            width: 100px;
            font-weight: bold;
            flex-shrink: 0;
            color: #606266;
          }
          
          span {
            flex: 1;
            color: #303133;
          }
        }
        
        .participant-tag {
          margin-right: 8px;
          margin-bottom: 5px;
          background-color: #f0f9eb;
          border-color: #e1f3d8;
          color: #67c23a;
        }
      }
      
      // 音频分析时间线样式优化
      .timeline-container {
        position: relative;
        padding-left: 20px;
        
        .timeline-item {
          position: relative;
          padding-bottom: 25px;
          
          .timeline-marker {
            position: absolute;
            left: -24px;
            top: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #409EFF;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1;
          }
          
          .timeline-content {
            background-color: #f5f7fa;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
            
            &:hover {
              background-color: #e4e7ed;
              transform: translateX(5px);
            }
            
            .element-info-item {
              display: flex;
              margin-bottom: 8px;
              line-height: 1.5;
              
              label {
                width: 60px;
                font-weight: bold;
                flex-shrink: 0;
                font-size: 13px;
                color: #606266;
              }
              
              span {
                flex: 1;
                font-size: 13px;
                color: #303133;
              }
            }
            
            .timestamp-tag {
              margin-left: 10px;
              background-color: #f0f9eb;
              border-color: #e1f3d8;
              color: #67c23a;
            }
          }
          
          &:last-child {
            padding-bottom: 0;
            
            &:after {
              display: none;
            }
          }
          
          &:after {
            content: '';
            position: absolute;
            left: -19px;
            top: 20px;
            width: 2px;
            height: calc(100% + 10px);
            background-color: #dcdfe6;
          }
        }
      }
      
      .visual-elements-container {
        padding: 10px 0;
      }
    }
    
    .analysis-result-content {
      pre {
        background-color: #f5f7fa;
        padding: 15px;
        border-radius: 4px;
        color: #333;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 14px;
        max-height: 400px;
        overflow-y: auto;
      }
    }
    
    .el-empty {
      padding: 20px 0;
    }
  }
  
  // 新增: 重新分析按钮样式
  .reanalyze-btn {
    background-color: #67c23a;
    border-color: #67c23a;
    // margin-bottom: 50px;
    margin-left: 30px;
    
    &:hover {
      background-color: #85ce61;
      border-color: #85ce61;
    }
    
    &:active {
      background-color: #5daf34;
      border-color: #5daf34;
    }
    
    .el-icon {
      margin-right: 5px;
    }
  }
  
  .el-form {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
    }
    
    // 添加表单项标签样式
    :deep(.el-form-item__label) {
      font-weight: normal;
      color: #606266;
    }
  }
  
  // 批量上传按钮样式
  .bulk-upload {
    display: inline-block;
    margin-left: 10px;
  }
  
  .el-table {
    margin-top: 10px;
    
    :deep(.el-table__cell) {
      transition: all 0.3s ease;
      padding: 12px 0;
    }
    
    :deep(.el-table__row) {
      height: 60px;
      
      &:hover {
        .el-table__cell {
          background-color: #ecf5ff !important;
        }
      }
    }
    
    :deep(.el-table__header .el-table__cell) {
      background-color: #f8f8f9;
      font-weight: bold;
    }
    
    .custom-audio-player {
      width: 100%;
      max-width: 400px;
      outline: none;
    }
    
    .audio-analysis-container {
      display: flex;
      justify-content: center;
    }
  }
  
  .action-btn {
    padding: 8px 10px;
    transition: all 0.3s;
    margin: 0 5px;
    font-size: 14px;
    
    .el-icon {
      transition: transform 0.3s;
      margin-right: 5px;
    }
    
    span {
      transition: all 0.3s;
    }
  }
  
  .el-pagination {
    justify-content: flex-end;
    margin-top: 20px;
  }
  
  .el-link {
    display: inline-flex;
    align-items: center;

    .el-icon {
      margin-right: 4px;
    }
  }

  /* URL链接样式 */
  .url-link {
    max-width: 100%;
    word-break: break-all;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;

    &:hover {
      text-decoration: underline;
    }
  }

  /* 当链接为空时的样式 */
  .disabled-link {
    color: #c0c4cc;
    cursor: not-allowed;
  }
  
  .upload-container {
    text-align: center;
    padding: 20px 0;
    
    .upload-area {
      border: 2px dashed #dcdfe6;
      border-radius: 6px;
      padding: 30px 0;
      cursor: pointer;
      transition: all 0.3s;
      margin-bottom: 20px;
      
      &:hover {
        border-color: #409eff;
        background-color: #f5f9ff;
      }
      
      .upload-icon {
        font-size: 48px;
        color: #c0c4cc;
        margin-bottom: 10px;
      }
      
      p {
        font-size: 14px;
        color: #606266;
        margin: 0;
      }
    }
    
    .file-selected {
      display: flex;
      align-items: center;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      padding: 15px;
      background-color: #f5f7fa;
      margin-bottom: 20px;

      .file-icon {
        font-size: 32px;
        color: #409eff;
        margin-right: 10px;
      }

      .file-info {
        flex: 1;
        text-align: left;

        .file-name {
          font-size: 14px;
          color: #303133;
          margin: 0;
          font-weight: 500;
          word-break: break-all;
        }

        .file-size {
          font-size: 12px;
          color: #909399;
          margin: 5px 0 0 0;
        }
      }

      .change-icon {
        font-size: 20px;
        color: #909399;
        cursor: pointer;
        transition: all 0.3s;
        padding: 5px;
        border-radius: 50%;

        &:hover {
          color: #409eff;
          background-color: #e4e7ed;
        }
      }
    }
  }
}

/* 自定义音频播放器样式 */
:deep(audio) {
  &::-webkit-media-controls-panel {
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  &::-webkit-media-controls-play-button {
    background-color: #409EFF;
    color: white;
    border-radius: 50%;
  }

  /* 进度条轨道样式 */
  &::-webkit-media-controls-timeline {
    background-color: rgba(219, 228, 236, 0.2);
    border-radius: 2px;
    margin: 0 10px;
  }

  /* 时间显示样式 */
  &::-webkit-media-controls-current-time-display,
  &::-webkit-media-controls-time-remaining-display {
    color: #606266;
    font-size: 12px;
  }
}

.dialog-footer {
  text-align: right;
}

// 新增: 弹窗标题样式
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
